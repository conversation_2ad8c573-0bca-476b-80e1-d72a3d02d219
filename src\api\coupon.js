import { http } from "@/utils/http";

// 新增优惠券
export const addCoupon = data => {
  return http.request(
    "post",
    "/organization/coupon/save",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 查询可使用优惠券课期
export const getCouponCourse = params => {
  return http.request(
    "get",
    "/organization/coursePeriod/findCouponAvailable",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 查询优惠券列表
export const getCouponFindAll = params => {
  return http.request(
    "get",
    "/organization/coupon/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 查询优惠券详情
export const getCouponFindById = params => {
  return http.request(
    "get",
    "/organization/coupon/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 停用使用优惠券
export const enabledCoupon = data => {
  return http.request(
    "post",
    "/organization/coupon/enabled",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 手动派发优惠券
export const distributeCoupon = data => {
  return http.request(
    "post",
    "/organization/coupon/distributeCoupon",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
