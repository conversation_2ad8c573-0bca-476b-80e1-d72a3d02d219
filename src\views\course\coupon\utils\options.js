import dayjs from "dayjs";
//有效优惠券列表
export const columns = [
  {
    label: "优惠券名",
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "发放时间（开始）",
    prop: "createdAt",
    minWidth: 180,
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "使用时间",
    prop: "createdAt",
    minWidth: 230,
    formatter: ({ useTime }) => {
      return useTime ? dayjs(useTime).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "领取数量/发放数量",
    prop: "educationLevel",
    minWidth: 90,
    formatter: ({ educationLevel }) => {
      return educationLevel || "--";
    }
  },
  {
    label: "优惠规则",
    prop: "major",
    minWidth: 90,
    formatter: ({ major }) => {
      return major || "--";
    }
  },
  {
    label: "类型",
    minWidth: 180,
    prop: "createdAt",
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "状态",
    prop: "courseName",
    minWidth: 200,
    formatter: ({ coursePeriodName }) => {
      return coursePeriodName || "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 320,
    slot: "operation"
  }
];
//已失效效优惠券列表
export const failureColumns = [
  {
    label: "优惠券名",
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "领取时间（开始）",
    prop: "createdAt",
    minWidth: 180,
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "使用时间",
    prop: "createdAt",
    minWidth: 230,
    formatter: ({ useTime }) => {
      return useTime ? dayjs(useTime).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "领取数量/发放数量",
    prop: "educationLevel",
    minWidth: 90,
    formatter: ({ educationLevel }) => {
      return educationLevel || "--";
    }
  },
  {
    label: "优惠规则",
    prop: "major",
    minWidth: 90,
    formatter: ({ major }) => {
      return major || "--";
    }
  },
  {
    label: "类型",
    minWidth: 180,
    prop: "createdAt",
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 150,
    slot: "operation"
  }
];
//优惠费用类型
export const discountType = [
  { name: "不限", id: 1 },
  { name: "课时", id: 2 },
  { name: "材料", id: 3 }
];
//状态
export const discountStatus = [
  { name: "全部", id: 1 },
  { name: "启用", id: 2 },
  { name: "停用", id: 3 }
];
//使用范围
export const scopeOptions = [
  { name: "不限", id: 1 },
  { name: "通用", id: 2 },
  { name: "指定", id: 3 }
];
